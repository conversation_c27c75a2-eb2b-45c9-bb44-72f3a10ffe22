import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);

interface GenerateSuggestionsRequest {
  fieldKey: string;
  currentValue: string;
  context: {
    fieldType: string;
    personalInfo: any;
    targetPosition: string;
    professionalSummary: string;
  };
  fieldType: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: GenerateSuggestionsRequest = await request.json();
    const { fieldKey, currentValue, context, fieldType } = body;

    if (!process.env.GOOGLE_AI_API_KEY) {
      return NextResponse.json(
        { error: 'Google AI API key not configured' },
        { status: 500 }
      );
    }

    const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });

    // Generate contextual prompt based on field type and key
    const prompt = generatePrompt(fieldKey, currentValue, context, fieldType);

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    console.log(text);

    // Parse the response to extract suggestions
    const suggestions = parseAISuggestions(text);

    return NextResponse.json({
      suggestions,
      fieldKey,
    });

  } catch (error) {
    console.error('Error generating AI suggestions:', error);
    return NextResponse.json(
      { error: 'Failed to generate suggestions' },
      { status: 500 }
    );
  }
}

function generatePrompt(fieldKey: string, currentValue: string, context: any, fieldType: string): string {
  const { personalInfo, targetPosition, professionalSummary } = context;
  
  let baseContext = `
Context Information:
- Target Position: ${targetPosition || 'Not specified'}
- Professional Summary: ${professionalSummary || 'Not provided'}
- Full Name: ${personalInfo?.fullName || 'Not provided'}
- Current Field Value: "${currentValue}"
`;

  switch (true) {
    case fieldKey === 'personalInfo.fullName':
      return `${baseContext}
Task: Generate 3 professional variations of the full name "${currentValue}" for a resume. Consider:
- Professional formatting variations
- Name ordering options (First Last, Last, First, etc.)
- Professional presentation styles

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;

    case fieldKey === 'personalInfo.email':
      return `${baseContext}
Task: Generate 3 professional email address suggestions based on the name "${personalInfo?.fullName || currentValue}". Consider:
- Professional email formats
- Different domain options (gmail.com, outlook.com, professional domains)
- Clean, ATS-friendly formats

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;

    case fieldKey === 'personalInfo.phone':
      return `${baseContext}
Task: Generate 3 professional phone number format variations for "${currentValue}". Consider:
- International formats
- Professional formatting styles
- ATS-friendly formats

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;

    case fieldKey === 'personalInfo.location':
      return `${baseContext}
Task: Generate 3 professional location format variations for "${currentValue}". Consider:
- Different levels of detail (City, State vs City, State, Country)
- Professional formatting
- ATS-friendly formats

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;

    case fieldKey === 'targetPosition':
      return `${baseContext}
Task: Generate 3 alternative professional job titles for "${currentValue}". Consider:
- Industry-standard variations
- Similar roles with different titles
- Career progression titles
- ATS-optimized keywords

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;

    case fieldKey === 'professionalSummary':
      return `${baseContext}
Task: Generate 3 professional summary variations for someone targeting "${targetPosition}". The current summary is: "${currentValue}"

Consider:
- 2-3 sentences highlighting key qualifications
- Industry-relevant keywords
- Achievement-focused language
- ATS-friendly formatting
- Professional tone

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;

    case fieldKey.startsWith('experience.') && fieldKey.includes('.jobTitle'):
      return `${baseContext}
Task: Generate 3 professional job title variations for "${currentValue}" in the context of targeting "${targetPosition}". Consider:
- Industry-standard titles
- Similar roles with different naming conventions
- ATS-optimized keywords
- Career progression alignment

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;

    case fieldKey.startsWith('experience.') && fieldKey.includes('.company'):
      return `${baseContext}
Task: Generate 3 professional company name format variations for "${currentValue}". Consider:
- Formal vs informal naming
- Company type inclusions (Inc., LLC, Corp.)
- Professional presentation styles

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;

    case fieldKey.startsWith('experience.') && fieldKey.includes('.responsibilities'):
      return `${baseContext}
Task: Generate 3 professional responsibility/achievement bullet points to replace "${currentValue}" for someone targeting "${targetPosition}". Consider:
- Action-verb starts
- Quantifiable achievements when possible
- Industry-relevant keywords
- Impact-focused language
- ATS-friendly formatting

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;

    default:
      return `${baseContext}
Task: Generate 3 professional improvements for the field "${fieldKey}" with current value "${currentValue}" for someone targeting "${targetPosition}". 
Consider professional language, ATS optimization, and industry standards.

Provide exactly 3 suggestions, one per line, no numbering or bullets:`;
  }
}

function parseAISuggestions(text: string): string[] {
  const lines = text.split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .filter(line => !line.match(/^[\d\-\*\•]\s*/)) // Remove numbered/bulleted items
    .slice(0, 3); // Take only first 3

  return lines.length > 0 ? lines : ['No suggestions available'];
}
