'use client';

import { useState, useCallback, useEffect } from 'react';
import type { JobMatchingResult } from '@/utils/ai-generators/jobMatchingGenerator';
import { useResume } from '@/hooks/useResume';
import { useForm } from '@/hooks/useForm';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useAuth } from '@/hooks/useAuth';
import { useFeedbackTracker } from '@/hooks/useFeedbackTracker';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import Seo from '@/components/Seo';
import Toast from '@/components/Toast';
import JobInfoInput from '@/components/JobInfoInput';
import FeedbackForm from '@/components/FeedbackForm';
import { authPost } from '@/lib/authFetch';
import { InputMethod } from '@/types/InputMethod';
import { createToast } from '@/utils/notificationUtils';

export default function JobMatchPage() {
  const auth = useAuth();
  const { trackEvent, logError } = useAnalytics();
  const { user, signIn, signUp, signInWithGoogle } = auth;
  
  // Track page view with error handling
  useEffect(() => {
    try {
      trackEvent('Job Match Page Viewed');
    } catch (error) {
      console.warn('Failed to track page view:', error);
    }
  }, [trackEvent]);
  // Use the resume hook
  const {
    isUploading,
    uploadSuccess,
    error,
    toast,
    existingResume,
    isLoading,
    isDeleting,
    isGettingResumeUrl,
    setToast,
    handleResumeUpload,
    handleViewResume,
    handleDeleteResume
  } = useResume(auth);
  
  // Menggunakan useForm hook untuk mengelola state form
  const form = useForm<{
    jobDescription: string;
    jobImage: File | null;
  }>({
    jobDescription: '',
    jobImage: null,
  });
  
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [applicationError, setApplicationError] = useState('');
  const [inputMethod, setInputMethod] = useState<InputMethod>(InputMethod.TEXT);

  // State untuk expandable sections
  const [expandedSections, setExpandedSections] = useState({
    skills: false,
    experience: false,
    education: false
  });
  
  // Use feedback tracker
  const { shouldShowFeedback, markFeedbackShown, incrementFeatureUsage } = useFeedbackTracker('job-match');
  const [showFeedback, setShowFeedback] = useState(false);

  // Login/Register modal state
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [loginMode, setLoginMode] = useState<'login' | 'register'>('register');
  const [loginForm, setLoginForm] = useState({ email: '', password: '', confirmPassword: '' });
  const [loginError, setLoginError] = useState('');
  const [loginSuccess, setLoginSuccess] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  // Password validation function
  const validatePassword = (password: string) => {
    return {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /\d/.test(password),
      hasSymbol: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
    };
  };

  // Get password validation status
  const passwordValidation = validatePassword(loginForm.password);
  
  // Hasil analisis yang diproses
  const [matchResult, setMatchResult] = useState<{
    overallMatch: number;
    skillsMatch: number;
    experienceMatch: number;
    educationMatch: number;
    analysis: string[];
    missingSkills: string[];
    strengths: string[];
    tips: string[];
    skillsAnalysis: string[];
    experienceAnalysis: string[];
    educationAnalysis: string[];
  } | null>(null);

  // Handle user login completion (for OAuth flow)
  useEffect(() => {
    if (user && showLoginModal) {
      // User just logged in, close modal
      setShowLoginModal(false);
      setLoginForm({ email: '', password: '', confirmPassword: '' });
      setLoginError('');
      setLoginSuccess('');
      // Show success toast when OAuth login completes
      setToast(createToast('Berhasil masuk', 'success'));
    }
  }, [user, showLoginModal]);

  // Handle login form submission
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoggingIn(true);
    setLoginError('');
    setLoginSuccess('');

    try {
      // For registration, check if passwords match
      if (loginMode === 'register' && loginForm.password !== loginForm.confirmPassword) {
        setLoginError('Password tidak cocok');
        setIsLoggingIn(false);
        return;
      }

      const result = loginMode === 'login'
        ? await signIn(loginForm.email, loginForm.password)
        : await signUp(loginForm.email, loginForm.password);

      if (result.success) {
        if (loginMode === 'register') {
          // Show email confirmation message and switch to login mode
          setLoginMode('login');
          setLoginForm({ email: loginForm.email, password: '', confirmPassword: '' });
          // Show success message for registration
          setLoginSuccess('Pendaftaran berhasil! Silakan periksa email Anda untuk konfirmasi, lalu kembali ke tab ini untuk masuk');
        } else {
          // For login success
          setShowLoginModal(false);
          setLoginForm({ email: '', password: '', confirmPassword: '' });
          // Show success toast
          setToast(createToast('Berhasil masuk', 'success'));
        }
      } else {
        setLoginError(result.message || 'Terjadi kesalahan saat masuk');
      }
    } catch (err) {
      setLoginError('Terjadi kesalahan saat masuk');
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle Google login
  const handleGoogleLogin = async () => {
    setIsLoggingIn(true);
    try {
      const result = await signInWithGoogle(encodeURIComponent(window.location.pathname));
      if (result.success && result.message) {
        // Open Google OAuth in new tab to preserve context
        window.open(result.message, '_blank');
        setLoginSuccess('Silakan selesaikan login di tab baru, lalu kembali ke halaman ini');
      }
      if (result.error) {
        if (result.message) {
          setLoginError(result.message);
        } else {
          throw result.error;
        }
      }
    } catch (error: any) {
      setLoginError('Gagal login dengan Google');
    } finally {
      setIsLoggingIn(false);
    }
  };
  
  // Handle job poster image upload
  const handleJobImageChange = (file: File | null) => {
    // Store the image file reference
    form.setValues(prev => ({ ...prev, jobImage: file }));
    
    // Clear any previous errors
    setApplicationError('');
  };
  
  // Toggle expandable section
  const toggleSection = useCallback((section: keyof typeof expandedSections) => {
    const newValue = !expandedSections[section];
    setExpandedSections(prev => ({
      ...prev,
      [section]: newValue
    }));
    
    // Track section toggle
    trackEvent('Job Match Section Toggled', {
      section: section,
      action: newValue ? 'expanded' : 'collapsed'
    });
  }, [expandedSections, trackEvent]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user is logged in first
    if (!user) {
      setLoginMode('register');
      setShowLoginModal(true);
      return;
    }

    // Increment feature usage count
    incrementFeatureUsage();

    // Set feedback form visibility based on usage count
    setShowFeedback(shouldShowFeedback);

    // Reset the match result when submitting new request
    setMatchResult(null);
    
    if (!existingResume) {
      setApplicationError('Harap unggah resume terlebih dahulu');
      trackEvent('Job Match Error', { error: 'no_resume' });
      return;
    }

    const { jobDescription, jobImage } = form.values;
    // Check that either a job description text or job image is provided
    if (inputMethod === InputMethod.TEXT && !jobDescription) {
      setApplicationError('Harap isi informasi lowongan');
      trackEvent('Job Match Error', { error: 'incomplete_form_text' });
      return;
    }
    if (inputMethod === InputMethod.IMAGE && !jobImage) {
      setApplicationError('Harap unggah poster lowongan');
      trackEvent('Job Match Error', { error: 'incomplete_form_image' });
      return;
    }
    
    // Track form submission
    trackEvent('Job Match Submission', {
      description_length: jobDescription.length,
      used_image: !!jobImage
    });

    setIsAnalyzing(true);
    setApplicationError('');

    try {
      const body = {
        ...(inputMethod === InputMethod.TEXT ? { jobDescription } : {}),
        ...(inputMethod === InputMethod.IMAGE ? { jobImage } : {}),
        ...(existingResume?.unauthenticatedResumeFile && existingResume?.fileName && { 
          unauthenticatedResumeFile: existingResume.unauthenticatedResumeFile,
          unauthenticatedResumeFileName: existingResume.fileName
        })
      };
      
      const response = await authPost('/api/generate-job-matching', body);

      const data = await response.json();
      if (data.success) {
        // Gunakan data terstruktur langsung dari respons API
        const result: JobMatchingResult = data.matchResult;
        
        setMatchResult(result);
        
        // Track successful analysis
        trackEvent('Job Match Analysis Completed', {
          overall_match: result.overallMatch,
          skills_match: result.skillsMatch,
          experience_match: result.experienceMatch,
          education_match: result.educationMatch,
          missing_skills_count: result.missingSkills.length,
          strengths_count: result.strengths.length,
          used_image: !!jobImage
        });
      } else {
        setApplicationError(data.error || 'Terjadi kesalahan saat menganalisis kecocokan pekerjaan');
        
        // Track failed analysis
        trackEvent('Job Match Analysis Failed', {
          error: data.error || 'unknown_error'
        });
      }
    } catch (err) {
      setApplicationError('Gagal menganalisis kecocokan pekerjaan. Silakan coba lagi.');
      console.error(err);
      
      // Log error to Rollbar
      logError(err instanceof Error ? err : new Error('Failed to analyze job match'), {
        feature: 'job_match',
        action: 'analyze',
        description_length: form.values.jobDescription.length
      }, 'error');
      
      // Track detailed analytics about the failure
      trackEvent('Job Match Analysis Exception', {
        error: err instanceof Error ? err.message : 'Unknown error',
        error_type: err instanceof Error ? err.constructor.name : 'Unknown'
      });
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  // Helper to get color for both SVG and bar
  const getMatchColor = (percentage: number, type: 'bg' | 'stroke' = 'bg') => {
    if (percentage >= 70) return type === 'bg' ? 'bg-green-500' : '#10B981';
    if (percentage >= 50) return type === 'bg' ? 'bg-yellow-500' : '#FBBF24';
    return type === 'bg' ? 'bg-red-500' : '#EF4444';
  };

  const renderMatchGauge = (percentage: number, label: string) => {
    const color = getMatchColor(percentage, 'bg');
    return (
      <div className="mb-4">
        <div className="flex justify-between mb-1">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          <span className="text-sm font-medium text-gray-700">{percentage}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div className={`${color} h-2.5 rounded-full`} style={{ width: `${percentage}%` }}></div>
        </div>
      </div>
    );
  };
  
  return (
    <main className="min-h-screen flex flex-col pt-16">
      <Toast 
        show={toast.show} 
        message={toast.message} 
        type={toast.type} 
        onClose={() => setToast({ ...toast, show: false })} 
      />
      <Seo 
        title="Analisis Kecocokan Lowongan Kerja dengan AI"
        description="Evaluasi seberapa cocok CV/resume Anda dengan persyaratan pekerjaan. Dapatkan analisis detail dan saran peningkatan untuk meningkatkan peluang Anda."
        canonical="https://gigsta.io/job-match"
      />
      <Navbar auth={auth} />

      {/* Sticky Banner for Unauthenticated Users */}
      {!auth.loading && !user && (
        <div className="sticky top-[4.5rem] z-10 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white py-2 sm:py-3 px-4 shadow-lg">
          <div className="max-w-7xl mx-auto flex items-center justify-between gap-3">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                <div className="relative">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-300 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                  </svg>
                  <div className="absolute inset-0 w-4 h-4 sm:w-5 sm:h-5 bg-yellow-300 rounded-full opacity-20 animate-ping"></div>
                </div>
                <span className="font-bold text-sm sm:text-base">🎉</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2 min-w-0">
                <span className="font-bold text-xs sm:text-sm md:text-base leading-tight">
                  BONUS 10 TOKEN GRATIS!
                </span>
                <span className="text-xs sm:text-sm opacity-90 leading-tight">
                  untuk pengguna baru yang mendaftar
                </span>
              </div>
            </div>
            <button
              onClick={() => {
                setLoginMode('register');
                setShowLoginModal(true)
              }}
              className="bg-white text-purple-600 hover:bg-yellow-100 font-bold py-1.5 px-3 sm:py-2 sm:px-4 rounded-full text-xs sm:text-sm text-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex-shrink-0"
            >
              Daftar Sekarang
            </button>
          </div>
        </div>
      )}

      {/* Feedback Modal */}
      {showFeedback && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
          <FeedbackForm
            featureType="job-match"
            onClose={() => {
              setShowFeedback(false);
              markFeedbackShown();
            }}
          />
        </div>
      )}

      {/* Login/Register Modal */}
      {showLoginModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex flex-row items-center justify-between gap-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {loginMode === 'login' ? 'Masuk ke Akun' : 'Daftar Akun Baru'}
                </h3>
                <button
                  onClick={() => {
                    setShowLoginModal(false);
                    setLoginError('');
                    setLoginSuccess('');
                    setLoginForm({ email: '', password: '', confirmPassword: '' });
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>

            <div className="px-6 py-4">
              <p className="text-sm text-gray-600 mb-4">
                {loginMode === 'login'
                  ? 'Masuk untuk menganalisis kecocokan lowongan Anda'
                  : 'Daftar untuk menganalisis kecocokan lowongan Anda'
                }
              </p>

              {loginSuccess && (
                <div className="mb-4 p-3 bg-green-100 border border-green-200 text-green-700 rounded-md text-sm">
                  {loginSuccess}
                </div>
              )}

              {loginError && (
                <div className="mb-4 p-3 bg-red-100 border border-red-200 text-red-700 rounded-md text-sm">
                  {loginError}
                </div>
              )}

              <form onSubmit={handleLogin} className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={loginForm.email}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                    disabled={isLoggingIn}
                  />
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={loginForm.password}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                    disabled={isLoggingIn}
                  />
                  {loginMode === 'register' && (
                    <div className="mt-2 space-y-1">
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${passwordValidation.minLength ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <span className={`text-xs ${passwordValidation.minLength ? 'text-green-600' : 'text-gray-500'}`}>
                          Minimal 8 karakter
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${passwordValidation.hasUppercase ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <span className={`text-xs ${passwordValidation.hasUppercase ? 'text-green-600' : 'text-gray-500'}`}>
                          Huruf besar (A-Z)
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${passwordValidation.hasLowercase ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <span className={`text-xs ${passwordValidation.hasLowercase ? 'text-green-600' : 'text-gray-500'}`}>
                          Huruf kecil (a-z)
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${passwordValidation.hasNumber ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <span className={`text-xs ${passwordValidation.hasNumber ? 'text-green-600' : 'text-gray-500'}`}>
                          Angka (0-9)
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${passwordValidation.hasSymbol ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <span className={`text-xs ${passwordValidation.hasSymbol ? 'text-green-600' : 'text-gray-500'}`}>
                          Simbol (!@#$%^&*)
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {loginMode === 'register' && (
                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                      Konfirmasi Password
                    </label>
                    <input
                      type="password"
                      id="confirmPassword"
                      value={loginForm.confirmPassword}
                      onChange={(e) => setLoginForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                      disabled={isLoggingIn}
                    />
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isLoggingIn}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoggingIn ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {loginMode === 'login' ? 'Masuk...' : 'Mendaftar...'}
                    </>
                  ) : (
                    loginMode === 'login' ? 'Masuk' : 'Daftar'
                  )}
                </button>
              </form>

              <div className="mt-4">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">atau</span>
                  </div>
                </div>

                <button
                  onClick={handleGoogleLogin}
                  disabled={isLoggingIn}
                  className="mt-3 w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Masuk dengan Google
                </button>
              </div>

              <div className="mt-4 text-center">
                <button
                  onClick={() => {
                    setLoginMode(loginMode === 'login' ? 'register' : 'login');
                    setLoginError('');
                    setLoginSuccess('');
                  }}
                  className="text-sm text-blue-600 hover:text-blue-500"
                  disabled={isLoggingIn}
                >
                  {loginMode === 'login'
                    ? 'Belum punya akun? Daftar di sini'
                    : 'Sudah punya akun? Masuk di sini'
                  }
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <section className="py-12 flex-grow">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Analisis Kecocokan Lowongan</h1>
            <p className="mt-4 text-lg text-gray-600">
              Temukan tingkat kecocokan CV/resume Anda dengan lowongan impian—dapatkan analisis instan dan saran peningkatan!
            </p>
          </div>
          
          <div className="bg-white shadow-md rounded-lg p-6 mb-8">
            {/* Section 1: Resume Upload */}
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-4">1. Upload CV/Resume Anda</h2>
              
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <svg className="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              ) : uploadSuccess && existingResume ? (
                <div className="border-2 border-green-200 rounded-lg p-4 sm:p-6 bg-green-50">
                  <div className="flex items-center mb-3">
                    <svg className="w-6 h-6 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <h3 className="font-medium text-green-700">Resume aktif tersedia!</h3>
                  </div>
                  
                  <div className="mb-4">
                    <p className="text-gray-700 mb-1 break-words">
                      File: <span className="font-mono text-sm">{existingResume.fileName.split('_').pop()}</span>
                    </p>
                    <p className="text-sm text-gray-500">
                      Diunggah pada: {new Date(existingResume.uploadedAt).toLocaleDateString('id-ID', { 
                        day: 'numeric', 
                        month: 'long', 
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                    {isGettingResumeUrl ? (
                      <div className="inline-flex items-center text-sm text-primary w-fit">
                        <svg className="animate-spin h-4 w-4 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Memuat...
                      </div>
                    ) : (
                      <button 
                        onClick={handleViewResume}
                        className="inline-flex items-center text-sm text-primary hover:underline bg-transparent border-0 p-0 cursor-pointer w-fit"
                      >
                        <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        Lihat Resume
                      </button>
                    )}
                    
                    {isUploading ? (
                      <div className="inline-flex items-center text-sm text-gray-800 w-fit">
                        <svg className="animate-spin h-4 w-4 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Mengunggah...
                      </div>
                    ) : (
                      <label
                        htmlFor="resume-existing"
                        className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800 hover:underline cursor-pointer w-fit"
                      >
                        <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                        </svg>
                        Unggah Resume Baru
                      </label>
                    )}
                    
                    {isDeleting ? (
                      <div className="inline-flex items-center text-sm text-red-600 w-fit">
                        <svg className="animate-spin h-4 w-4 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Menghapus...
                      </div>
                    ) : (
                      <button
                        onClick={handleDeleteResume}
                        className="inline-flex items-center text-sm text-red-600 hover:text-red-800 hover:underline w-fit"
                        type="button"
                      >
                        <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Hapus Resume
                      </button>
                    )}
                    
                    <input
                      type="file"
                      id="resume-existing"
                      accept=".pdf,.docx,.png,.jpg,.jpeg"
                      className="hidden"
                      onChange={handleResumeUpload}
                      ref={(input) => {
                        if (input) {
                          input.value = '';
                        }
                      }}
                    />
                  </div>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <input
                    type="file"
                    id="resume-new"
                    accept=".pdf,.docx,.png,.jpg,.jpeg"
                    className="hidden"
                    onChange={handleResumeUpload}
                    disabled={isUploading}
                    ref={(input) => {
                      if (input) {
                        input.value = '';
                      }
                    }}
                  />
                  <label
                    htmlFor="resume-new"
                    className={`cursor-pointer flex flex-col items-center justify-center`}
                  >
                    {isUploading ? (
                      <svg className="animate-spin h-12 w-12 text-primary mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      <svg className="w-12 h-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                      </svg>
                    )}
                    {isUploading ? (
                      <p className="text-gray-700 font-medium">Mengunggah Resume...</p>
                    ) : (
                      <>
                        <p className="text-gray-700 font-medium">Klik untuk mengunggah resume Anda</p>
                        <p className="text-sm text-gray-500 mt-1">PDF, DOCX, PNG, JPG, atau JPEG (Maks. 5MB)</p>
                      </>
                    )}
                  </label>
                </div>
              )}
              
              {error && <p className="text-red-500 mt-2 text-sm">{error}</p>}

              {/* Info catatan jika user tidak login dan sudah upload resume */}
              {!user && existingResume && (
                <p className="mt-2 text-xs text-gray-500">
                  *File CV/resume Anda disimpan secara lokal dan akan hilang jika halaman di-refresh. <b>Masuk</b> agar CV/resume tetap tersimpan sehingga tidak perlu mengunggah ulang
                </p>
              )}
            </div>

            {/* Section 2: Job Information Input */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h2 className="text-xl font-semibold mb-4">2. Masukkan Informasi Lowongan</h2>
              <JobInfoInput
                jobDescription={form.values.jobDescription}
                onJobDescriptionChange={(value) => form.setValues(prev => ({ ...prev, jobDescription: value }))}
                jobImage={form.values.jobImage}
                onJobImageChange={handleJobImageChange}
                onInputMethodChange={setInputMethod}
                onError={setApplicationError}
              />
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <button
                className={`btn-primary w-full ${isAnalyzing ? 'opacity-75 cursor-not-allowed' : ''}`}
                onClick={handleSubmit}
                disabled={isAnalyzing || isUploading || isLoading}
              >
                {isAnalyzing ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Menganalisis Kecocokan...
                  </span>
                ) : (
                  <span className="flex items-center justify-center">
                    Analisis Kecocokan - 
                    <svg className="w-4 h-4 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
                      <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
                      <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
                      <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
                    </svg>
                    0
                  </span>
                )}
              </button>
            </div>

            {/* Error message if present */}
            {applicationError && (
              <div className="p-3 mt-6 text-sm bg-red-100 border border-red-200 text-red-800 rounded-md">
                <div className="flex">
                  <svg className="h-5 w-5 text-red-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <div className="flex-1">
                    <span>{applicationError}</span>
                    {applicationError.includes('Token Anda tidak cukup') && (
                      <div className="mt-3">
                        <button
                          onClick={() => window.location.href = '/buy-tokens'}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          Beli Token
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Match Results */}
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Hasil Analisis</h2>
            
            {!matchResult ? (
              <div className="text-gray-400 italic text-center flex flex-col items-center justify-center h-[300px]">
                <svg className="h-12 w-12 mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <p>Masukkan posisi dan deskripsi pekerjaan dan klik "Analisis Kecocokan" untuk melihat seberapa cocok CV/resume Anda dengan posisi tersebut.</p>
              </div>
            ) : (
              <div>

                <div className="flex justify-center items-center mb-8">
                  <div className="relative">
                    <svg className="w-32 h-32 block mx-auto" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="18" cy="18" r="16" fill="none" stroke="#e0e0e0" strokeWidth="2"></circle>
                      <circle 
                        cx="18" 
                        cy="18" 
                        r="16" 
                        fill="none" 
                        stroke={getMatchColor(matchResult.overallMatch, 'stroke')}
                        strokeWidth="2" 
                        strokeDasharray={`${(matchResult.overallMatch/100) * 100} 100`}
                        strokeLinecap="round"
                        transform="rotate(-90 18 18)"
                      ></circle>
                      <text x="18" y="18" textAnchor="middle" dominantBaseline="central" fontSize="8" fontWeight="bold" fill="#333">
                        {matchResult.overallMatch}%
                      </text>
                    </svg>
                    <p className="text-center font-medium mt-2">Kecocokan Keseluruhan</p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  {renderMatchGauge(matchResult.skillsMatch, 'Keterampilan')}
                  {renderMatchGauge(matchResult.experienceMatch, 'Pengalaman')}
                  {renderMatchGauge(matchResult.educationMatch, 'Pendidikan')}
                </div>
                
                <div className="mt-6 space-y-6">
                  {/* Analisis Keseluruhan */}
                  <div>
                    <h3 className="font-medium text-gray-800 mb-2">Analisis Keseluruhan</h3>
                    <ul className="space-y-2 list-disc pl-5 text-sm text-gray-600">
                      {matchResult.analysis.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Keterampilan - Expandable */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <button 
                      className="w-full flex justify-between items-center bg-gray-50 px-4 py-3 text-left focus:outline-none" 
                      onClick={() => toggleSection('skills')}
                    >
                      <div className="flex items-center">
                        <h3 className="font-medium text-gray-800">Analisis Keterampilan</h3>
                        <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                          {matchResult.skillsMatch}%
                        </span>
                      </div>
                      <svg 
                        className={`w-5 h-5 transition-transform ${expandedSections.skills ? 'transform rotate-180' : ''}`} 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </button>
                    {expandedSections.skills && (
                      <div className="px-4 py-3 border-t border-gray-200">
                        <ul className="space-y-2 list-disc pl-5 text-sm text-gray-600">
                          {matchResult.skillsAnalysis.map((item, index) => (
                            <li key={index}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Pengalaman - Expandable */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <button 
                      className="w-full flex justify-between items-center bg-gray-50 px-4 py-3 text-left focus:outline-none" 
                      onClick={() => toggleSection('experience')}
                    >
                      <div className="flex items-center">
                        <h3 className="font-medium text-gray-800">Analisis Pengalaman</h3>
                        <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                          {matchResult.experienceMatch}%
                        </span>
                      </div>
                      <svg 
                        className={`w-5 h-5 transition-transform ${expandedSections.experience ? 'transform rotate-180' : ''}`} 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </button>
                    {expandedSections.experience && (
                      <div className="px-4 py-3 border-t border-gray-200">
                        <ul className="space-y-2 list-disc pl-5 text-sm text-gray-600">
                          {matchResult.experienceAnalysis.map((item, index) => (
                            <li key={index}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Pendidikan - Expandable */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <button 
                      className="w-full flex justify-between items-center bg-gray-50 px-4 py-3 text-left focus:outline-none" 
                      onClick={() => toggleSection('education')}
                    >
                      <div className="flex items-center">
                        <h3 className="font-medium text-gray-800">Analisis Pendidikan</h3>
                        <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                          {matchResult.educationMatch}%
                        </span>
                      </div>
                      <svg 
                        className={`w-5 h-5 transition-transform ${expandedSections.education ? 'transform rotate-180' : ''}`} 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </button>
                    {expandedSections.education && (
                      <div className="px-4 py-3 border-t border-gray-200">
                        <ul className="space-y-2 list-disc pl-5 text-sm text-gray-600">
                          {matchResult.educationAnalysis.map((item, index) => (
                            <li key={index}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Tips Peningkatan - Selalu Ditampilkan */}
                  <div className="border-l-4 border-yellow-400 bg-yellow-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-800 mb-2 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      Tips Peningkatan
                    </h3>
                    <ul className="space-y-2 list-disc pl-5 text-sm text-gray-600">
                      {matchResult.tips.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-6 mt-6">
                  <div>
                    <h3 className="font-medium text-gray-800 mb-3">Keterampilan yang Kurang</h3>
                    <div className="flex flex-wrap">
                      {matchResult.missingSkills.map((skill, index) => (
                        <span key={index} className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded mr-2 mb-2">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-gray-800 mb-3">Kekuatan Anda</h3>
                    <div className="flex flex-wrap">
                      {matchResult.strengths.map((skill, index) => (
                        <span key={index} className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded mr-2 mb-2">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {matchResult && (
              <p className="text-xs text-gray-500 italic mt-4">
                *Hasil analisis ini dibuat dengan AI dan mungkin tidak sepenuhnya akurat. Silakan periksa ulang. Anda dapat generate ulang berkali-kali untuk hasil yang lebih sesuai.
              </p>
            )}
          </div>
          
          {matchResult && (
            <div className="mt-8 text-center">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/email-application" className="btn-primary">
                  Buat Email Lamaran
                </a>
                <a href="/application-letter" className="btn-secondary">
                  Buat Surat Lamaran
                </a>
              </div>
            </div>
          )}
        </div>
      </section>
      
      <Footer />
    </main>
  );
}
